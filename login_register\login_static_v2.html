<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>App Login Rewards - Static Version</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0F1419 0%, #1C2833 50%, #2C3E50 100%);
            min-height: 100vh;
            color: white;
        }

        .login-container {
            max-width: 400px;
            margin: 0 auto;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* 顶部奖励区域 - 左右布局 */
        .reward-header {
            padding: 60px 20px 40px;
            background: linear-gradient(135deg, rgba(255,215,0,0.1) 0%, rgba(255,165,0,0.05) 100%);
            border-radius: 0 0 30px 30px;
            position: relative;
        }

        .reward-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 20px;
        }

        .reward-text {
            flex: 1;
            text-align: left;
        }

        .main-title {
            font-size: 24px;
            font-weight: bold;
            color: #FFD700;
            margin-bottom: 12px;
            line-height: 1.3;
        }

        .subtitle {
            font-size: 16px;
            color: #E8E8E8;
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .points-info {
            font-size: 14px;
            color: #B0B0B0;
        }

        .points-highlight {
            color: #FFD700;
            font-weight: bold;
            font-size: 18px;
        }

        .reward-image {
            flex-shrink: 0;
            width: 120px;
            height: 120px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .reward-circle {
            width: 100px;
            height: 100px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 25px rgba(255,215,0,0.3);
            position: relative;
        }

        .reward-icon {
            font-size: 48px;
            color: #1C2833;
        }

        .floating-coins {
            position: absolute;
            width: 20px;
            height: 20px;
            background: #FFD700;
            border-radius: 50%;
            opacity: 0.8;
        }

        .coin-1 {
            top: -10px;
            right: 10px;
            width: 16px;
            height: 16px;
        }

        .coin-2 {
            bottom: -5px;
            left: 5px;
            width: 12px;
            height: 12px;
        }

        .coin-3 {
            top: 20px;
            right: -8px;
            width: 14px;
            height: 14px;
        }

        /* 登录表单区域 */
        .login-form-area {
            flex: 1;
            padding: 40px 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .login-form {
            background: rgba(255,255,255,0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255,255,255,0.1);
            border-radius: 20px;
            padding: 35px;
            margin-bottom: 30px;
        }

        .form-title {
            text-align: center;
            font-size: 22px;
            color: #FFD700;
            margin-bottom: 30px;
            font-weight: 600;
        }

        .input-group {
            margin-bottom: 25px;
            position: relative;
        }

        .input-field {
            width: 100%;
            padding: 18px 20px;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 12px;
            color: white;
            font-size: 16px;
        }

        .input-field::placeholder {
            color: rgba(255,255,255,0.6);
        }

        .login-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            font-size: 14px;
        }

        .remember-me {
            display: flex;
            align-items: center;
            color: #B0B0B0;
        }

        .remember-me input {
            margin-right: 8px;
        }

        .forgot-password {
            color: #FFD700;
            text-decoration: none;
        }

        .login-btn {
            width: 100%;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: #1C2833;
            border: none;
            padding: 20px;
            border-radius: 12px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 0 8px 25px rgba(255,215,0,0.3);
            margin-bottom: 25px;
        }

        .divider {
            text-align: center;
            margin: 25px 0;
            position: relative;
            color: #B0B0B0;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: rgba(255,255,255,0.2);
        }

        .divider span {
            background: rgba(255,255,255,0.05);
            padding: 0 15px;
        }

        .social-login {
            display: flex;
            gap: 15px;
            margin-bottom: 25px;
        }

        .social-btn {
            flex: 1;
            padding: 15px;
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 12px;
            background: rgba(255,255,255,0.05);
            color: white;
            text-decoration: none;
            text-align: center;
            font-size: 14px;
        }

        .register-link {
            text-align: center;
            color: #B0B0B0;
            font-size: 14px;
        }

        .register-link a {
            color: #FFD700;
            text-decoration: none;
            font-weight: 600;
        }

        /* 底部区域 */
        .footer-area {
            padding: 25px 20px;
            text-align: center;
            border-top: 1px solid rgba(255,255,255,0.1);
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 25px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .footer-links a {
            color: #B0B0B0;
            text-decoration: none;
            font-size: 12px;
        }

        .copyright {
            color: #808080;
            font-size: 11px;
        }

        /* 响应式设计 */
        @media (max-width: 375px) {
            .reward-content {
                flex-direction: column;
                text-align: center;
                gap: 25px;
            }
            
            .reward-text {
                text-align: center;
            }
            
            .main-title {
                font-size: 22px;
            }
            
            .reward-image {
                width: 100px;
                height: 100px;
            }
            
            .reward-circle {
                width: 80px;
                height: 80px;
            }
            
            .reward-icon {
                font-size: 36px;
            }
        }

        @media (min-width: 768px) {
            .login-container {
                max-width: 500px;
            }
            
            .main-title {
                font-size: 28px;
            }
            
            .reward-image {
                width: 140px;
                height: 140px;
            }
            
            .reward-circle {
                width: 120px;
                height: 120px;
            }
            
            .reward-icon {
                font-size: 56px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- 顶部奖励区域 - 左右布局 -->
        <div class="reward-header">
            <div class="reward-content">
                <div class="reward-text">
                    <h1 class="main-title">Login to App & Earn Points</h1>
                    <p class="subtitle">Get exclusive rewards for mobile login</p>
                    <div class="points-info">
                        Earn <span class="points-highlight">500 Points</span><br>
                        = Watch 5 Premium Shows
                    </div>
                </div>
                
                <div class="reward-image">
                    <div class="reward-circle">
                        <div class="reward-icon">🎁</div>
                    </div>
                    <div class="floating-coins coin-1"></div>
                    <div class="floating-coins coin-2"></div>
                    <div class="floating-coins coin-3"></div>
                </div>
            </div>
        </div>
        
        <!-- 登录表单区域 -->
        <div class="login-form-area">
            <div class="login-form">
                <h2 class="form-title">Start Your Journey</h2>
                
                <div class="input-group">
                    <input type="text" class="input-field" placeholder="📱 Phone Number">
                </div>
                
                <div class="input-group">
                    <input type="password" class="input-field" placeholder="🔒 Password">
                </div>
                
                <div class="login-options">
                    <label class="remember-me">
                        <input type="checkbox"> Remember me
                    </label>
                    <a href="#" class="forgot-password">Forgot password?</a>
                </div>
                
                <button class="login-btn">
                    Login Now & Get 500 Points
                </button>
                
                <div class="divider">
                    <span>or</span>
                </div>
                
                <div class="social-login">
                    <a href="#" class="social-btn">📱 WeChat</a>
                    <a href="#" class="social-btn">📞 SMS</a>
                </div>
                
                <div class="register-link">
                    Don't have an account? <a href="#">Sign up</a>
                </div>
            </div>
        </div>
        
        <!-- 底部区域 -->
        <div class="footer-area">
            <div class="footer-links">
                <a href="#">Terms</a>
                <a href="#">Privacy</a>
                <a href="#">Help</a>
                <a href="#">Contact</a>
            </div>
            <div class="copyright">
                © 2024 Streaming Platform. All rights reserved.
            </div>
        </div>
    </div>
</body>
</html>
