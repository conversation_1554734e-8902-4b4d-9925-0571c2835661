<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>App Login Rewards - Dynamic Version</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0F1419 0%, #1C2833 50%, #2C3E50 100%);
            min-height: 100vh;
            color: white;
            overflow-x: hidden;
        }

        .login-container {
            max-width: 400px;
            margin: 0 auto;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* 顶部奖励区域 - 左右布局动态版本 */
        .reward-header {
            padding: 60px 20px 40px;
            background: linear-gradient(135deg, rgba(255,215,0,0.1) 0%, rgba(255,165,0,0.05) 100%);
            border-radius: 0 0 30px 30px;
            position: relative;
            overflow: hidden;
        }

        .reward-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,215,0,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        .reward-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 20px;
            position: relative;
            z-index: 2;
        }

        .reward-text {
            flex: 1;
            text-align: left;
            animation: slideInLeft 1s ease-out;
        }

        .main-title {
            font-size: 24px;
            font-weight: bold;
            background: linear-gradient(45deg, #FFD700, #FFA500, #FFD700);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientShift 3s ease-in-out infinite;
            margin-bottom: 12px;
            line-height: 1.3;
        }

        .subtitle {
            font-size: 16px;
            color: #E8E8E8;
            margin-bottom: 15px;
            line-height: 1.4;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .points-info {
            font-size: 14px;
            color: #B0B0B0;
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        .points-highlight {
            color: #FFD700;
            font-weight: bold;
            font-size: 18px;
            animation: pulse 2s infinite 1s;
        }

        .reward-image {
            flex-shrink: 0;
            width: 120px;
            height: 120px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: slideInRight 1s ease-out;
        }

        .reward-circle {
            width: 100px;
            height: 100px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 25px rgba(255,215,0,0.3);
            position: relative;
            animation: float 3s ease-in-out infinite, glow 2s ease-in-out infinite alternate;
        }

        .reward-icon {
            font-size: 48px;
            color: #1C2833;
            animation: bounce 2s infinite 1.5s;
        }

        .floating-coins {
            position: absolute;
            background: #FFD700;
            border-radius: 50%;
            opacity: 0.8;
            animation: coinFloat 3s ease-in-out infinite;
        }

        .coin-1 {
            top: -10px;
            right: 10px;
            width: 16px;
            height: 16px;
            animation-delay: 0.5s;
        }

        .coin-2 {
            bottom: -5px;
            left: 5px;
            width: 12px;
            height: 12px;
            animation-delay: 1s;
        }

        .coin-3 {
            top: 20px;
            right: -8px;
            width: 14px;
            height: 14px;
            animation-delay: 1.5s;
        }

        /* 登录表单区域 - 动态版本 */
        .login-form-area {
            flex: 1;
            padding: 40px 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            animation: fadeInUp 1s ease-out 1.5s both;
        }

        .login-form {
            background: rgba(255,255,255,0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255,255,255,0.1);
            border-radius: 20px;
            padding: 35px;
            margin-bottom: 30px;
            animation: slideInUp 0.8s ease-out 2s both;
        }

        .form-title {
            text-align: center;
            font-size: 22px;
            color: #FFD700;
            margin-bottom: 30px;
            font-weight: 600;
            animation: glow 2s ease-in-out infinite alternate;
        }

        .input-group {
            margin-bottom: 25px;
            position: relative;
            animation: slideInLeft 0.6s ease-out both;
        }

        .input-group:nth-child(2) { animation-delay: 2.3s; }
        .input-group:nth-child(3) { animation-delay: 2.6s; }

        .input-field {
            width: 100%;
            padding: 18px 20px;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 12px;
            color: white;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .input-field:focus {
            outline: none;
            border-color: #FFD700;
            box-shadow: 0 0 0 3px rgba(255,215,0,0.2);
            background: rgba(255,255,255,0.15);
            transform: scale(1.02);
        }

        .input-field::placeholder {
            color: rgba(255,255,255,0.6);
        }

        .login-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            font-size: 14px;
            animation: fadeIn 0.8s ease-out 2.9s both;
        }

        .remember-me {
            display: flex;
            align-items: center;
            color: #B0B0B0;
        }

        .remember-me input {
            margin-right: 8px;
        }

        .forgot-password {
            color: #FFD700;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .forgot-password:hover {
            color: #FFA500;
        }

        .login-btn {
            width: 100%;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: #1C2833;
            border: none;
            padding: 20px;
            border-radius: 12px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 0 8px 25px rgba(255,215,0,0.3);
            margin-bottom: 25px;
            transition: all 0.3s ease;
            animation: buttonPulse 2s infinite, slideInUp 0.8s ease-out 3.2s both;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(255,215,0,0.4);
        }

        .divider {
            text-align: center;
            margin: 25px 0;
            position: relative;
            color: #B0B0B0;
            animation: fadeIn 0.8s ease-out 3.5s both;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: rgba(255,255,255,0.2);
        }

        .divider span {
            background: rgba(255,255,255,0.05);
            padding: 0 15px;
        }

        .social-login {
            display: flex;
            gap: 15px;
            margin-bottom: 25px;
            animation: slideInUp 0.8s ease-out 3.8s both;
        }

        .social-btn {
            flex: 1;
            padding: 15px;
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 12px;
            background: rgba(255,255,255,0.05);
            color: white;
            text-decoration: none;
            text-align: center;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .social-btn:hover {
            background: rgba(255,255,255,0.1);
            border-color: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .register-link {
            text-align: center;
            color: #B0B0B0;
            font-size: 14px;
            animation: fadeIn 0.8s ease-out 4.1s both;
        }

        .register-link a {
            color: #FFD700;
            text-decoration: none;
            font-weight: 600;
        }

        .register-link a:hover {
            color: #FFA500;
        }

        /* 底部区域 */
        .footer-area {
            padding: 25px 20px;
            text-align: center;
            border-top: 1px solid rgba(255,255,255,0.1);
            animation: fadeInUp 1s ease-out 4.5s both;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 25px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .footer-links a {
            color: #B0B0B0;
            text-decoration: none;
            font-size: 12px;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: #FFD700;
        }

        .copyright {
            color: #808080;
            font-size: 11px;
        }

        /* 动画定义 */
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideInLeft {
            from {
                transform: translateX(-50px);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideInRight {
            from {
                transform: translateX(50px);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideInUp {
            from {
                transform: translateY(50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-8px);
            }
            60% {
                transform: translateY(-4px);
            }
        }

        @keyframes glow {
            from {
                box-shadow: 0 8px 25px rgba(255,215,0,0.3);
            }
            to {
                box-shadow: 0 8px 25px rgba(255,215,0,0.6), 0 0 30px rgba(255,215,0,0.3);
            }
        }

        @keyframes buttonPulse {
            0%, 100% {
                box-shadow: 0 8px 25px rgba(255,215,0,0.3);
            }
            50% {
                box-shadow: 0 8px 25px rgba(255,215,0,0.5), 0 0 0 5px rgba(255,215,0,0.1);
            }
        }

        @keyframes coinFloat {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.8;
            }
            50% {
                transform: translateY(-15px) rotate(180deg);
                opacity: 1;
            }
        }

        /* 响应式设计 */
        @media (max-width: 375px) {
            .reward-content {
                flex-direction: column;
                text-align: center;
                gap: 25px;
            }
            
            .reward-text {
                text-align: center;
            }
            
            .main-title {
                font-size: 22px;
            }
            
            .reward-image {
                width: 100px;
                height: 100px;
            }
            
            .reward-circle {
                width: 80px;
                height: 80px;
            }
            
            .reward-icon {
                font-size: 36px;
            }
        }

        @media (min-width: 768px) {
            .login-container {
                max-width: 500px;
            }
            
            .main-title {
                font-size: 28px;
            }
            
            .reward-image {
                width: 140px;
                height: 140px;
            }
            
            .reward-circle {
                width: 120px;
                height: 120px;
            }
            
            .reward-icon {
                font-size: 56px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- 顶部奖励区域 - 左右布局动态版本 -->
        <div class="reward-header">
            <div class="reward-content">
                <div class="reward-text">
                    <h1 class="main-title">Login to App & Earn Points</h1>
                    <p class="subtitle">Get exclusive rewards for mobile login</p>
                    <div class="points-info">
                        Earn <span class="points-highlight">500 Points</span><br>
                        = Watch 5 Premium Shows
                    </div>
                </div>
                
                <div class="reward-image">
                    <div class="reward-circle">
                        <div class="reward-icon">🎁</div>
                    </div>
                    <div class="floating-coins coin-1"></div>
                    <div class="floating-coins coin-2"></div>
                    <div class="floating-coins coin-3"></div>
                </div>
            </div>
        </div>
        
        <!-- 登录表单区域 -->
        <div class="login-form-area">
            <div class="login-form">
                <h2 class="form-title">Start Your Journey</h2>
                
                <div class="input-group">
                    <input type="text" class="input-field" placeholder="📱 Phone Number">
                </div>
                
                <div class="input-group">
                    <input type="password" class="input-field" placeholder="🔒 Password">
                </div>
                
                <div class="login-options">
                    <label class="remember-me">
                        <input type="checkbox"> Remember me
                    </label>
                    <a href="#" class="forgot-password">Forgot password?</a>
                </div>
                
                <button class="login-btn" onclick="simulateLogin()">
                    Login Now & Get 500 Points
                </button>
                
                <div class="divider">
                    <span>or</span>
                </div>
                
                <div class="social-login">
                    <a href="#" class="social-btn">📱 WeChat</a>
                    <a href="#" class="social-btn">📞 SMS</a>
                </div>
                
                <div class="register-link">
                    Don't have an account? <a href="#">Sign up</a>
                </div>
            </div>
        </div>
        
        <!-- 底部区域 -->
        <div class="footer-area">
            <div class="footer-links">
                <a href="#">Terms</a>
                <a href="#">Privacy</a>
                <a href="#">Help</a>
                <a href="#">Contact</a>
            </div>
            <div class="copyright">
                © 2024 Streaming Platform. All rights reserved.
            </div>
        </div>
    </div>

    <script>
        // 模拟登录功能
        function simulateLogin() {
            const btn = document.querySelector('.login-btn');
            const originalText = btn.innerHTML;
            
            btn.innerHTML = 'Logging in...';
            btn.style.background = 'linear-gradient(45deg, #ccc, #999)';
            btn.disabled = true;
            
            setTimeout(() => {
                btn.innerHTML = '✅ Success! 500 Points Earned';
                btn.style.background = 'linear-gradient(45deg, #4CAF50, #45a049)';
                
                showSuccessMessage();
                
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.style.background = 'linear-gradient(45deg, #FFD700, #FFA500)';
                    btn.disabled = false;
                }, 3000);
            }, 2000);
        }

        // 显示成功消息
        function showSuccessMessage() {
            const message = document.createElement('div');
            message.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: linear-gradient(45deg, #4CAF50, #45a049);
                color: white;
                padding: 20px 30px;
                border-radius: 15px;
                font-size: 18px;
                font-weight: bold;
                z-index: 10000;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                animation: successPop 0.5s ease-out;
                text-align: center;
            `;
            message.innerHTML = '🎉 Congratulations!<br>500 Points Earned<br>Watch 5 Premium Shows Now!';
            
            document.body.appendChild(message);
            
            setTimeout(() => {
                message.style.animation = 'successPop 0.5s ease-out reverse';
                setTimeout(() => {
                    document.body.removeChild(message);
                }, 500);
            }, 2500);
        }

        // 添加成功弹窗动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes successPop {
                0% {
                    transform: translate(-50%, -50%) scale(0.5);
                    opacity: 0;
                }
                100% {
                    transform: translate(-50%, -50%) scale(1);
                    opacity: 1;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
